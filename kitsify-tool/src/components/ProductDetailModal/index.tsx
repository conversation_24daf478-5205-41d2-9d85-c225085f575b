import type { Product, ProductDuration } from '@/services/products';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import React from 'react';
import TipTapViewer from '../TipTapViewer';

type ProductDetailModalProps = {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
  selectedDuration: ProductDuration;
  quantity: number;
  onDurationChange: (durationId: number) => void;
  onQuantityChange: (quantity: number) => void;
  onAddToCart: () => void;
};

const ProductDetailModal: React.FC<ProductDetailModalProps> = ({
  product,
  isOpen,
  onClose,
  selectedDuration,
  quantity,
  onDurationChange,
  onQuantityChange,
  onAddToCart,
}) => {
  const t = useTranslations('Shop');

  if (!isOpen) {
    return null;
  }

  const {
    name,
    description,
    image_url,
    features,
    durations = [],
  } = product;

  const isOutOfStock = selectedDuration.quantity <= 0;
  const hasDiscount = selectedDuration.discount_price > 0
    && selectedDuration.discount_price < selectedDuration.original_price;
  const displayPrice = hasDiscount
    ? Number(selectedDuration.discount_price)
    : Number(selectedDuration.original_price);

  const handleIncrement = () => {
    if (quantity < selectedDuration.quantity) {
      onQuantityChange(quantity + 1);
    }
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      onQuantityChange(quantity - 1);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black bg-opacity-50 p-4">
      <div className="relative max-h-[95vh] w-full max-w-7xl overflow-auto rounded-lg bg-white shadow-xl">
        {/* Close button */}
        <button
          type="button"
          onClick={onClose}
          className="absolute right-4 top-4 z-10 rounded-full bg-white p-2 text-gray-500 shadow-md transition-all hover:bg-gray-100 hover:text-gray-700"
          aria-label="Close"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="size-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div className="flex flex-col lg:flex-row">
          {/* Product Image */}
          <div className="w-full p-6 lg:w-1/2">
            <div className="relative aspect-square w-full overflow-hidden rounded-lg shadow-md">
              {image_url
                ? (
                    <Image
                      src={image_url}
                      alt={name}
                      fill
                      className="object-cover"
                    />
                  )
                : (
                    <div className="flex size-full items-center justify-center bg-gray-200">
                      <span className="text-gray-400">No image</span>
                    </div>
                  )}
              {hasDiscount && (
                <div className="absolute left-0 top-0 m-2 rounded-full bg-red-500 px-3 py-1 text-sm font-bold text-white">
                  -
                  {Math.round(((Number(selectedDuration.original_price) - Number(selectedDuration.discount_price))
                    / Number(selectedDuration.original_price)) * 100)}
                  %
                </div>
              )}
            </div>
          </div>

          {/* Product Info */}
          <div className="w-full p-6 lg:w-1/2">
            <h2 className="mb-4 text-2xl font-bold text-gray-800">{name}</h2>

            {/* Price */}
            <div className="mb-6">
              <div className="flex items-center gap-3">
                <span className={`text-3xl font-bold ${hasDiscount ? 'text-red-600' : 'text-gray-900'}`}>
                  $
                  {displayPrice.toFixed(2)}
                </span>
                {hasDiscount && (
                  <span className="text-xl text-gray-500 line-through">
                    $
                    {Number(selectedDuration.original_price).toFixed(2)}
                  </span>
                )}
              </div>
              <div className="mt-1 text-sm text-gray-600">
                {selectedDuration.duration_days}
                {' '}
                {t('days') || 'ngày'}
              </div>
            </div>

            {/* Duration Selection */}
            {durations.length > 1 && (
              <div className="mb-6">
                <label className="mb-3 block text-sm font-medium text-gray-700">
                  {t('duration') || 'Thời hạn'}
                  :
                </label>
                <div className="grid gap-3 md:grid-cols-2">
                  {durations.map((duration) => {
                    const isSelected = selectedDuration?.id === duration.id;
                    const durationPrice = Number(duration.discount_price > 0 ? duration.discount_price : duration.original_price);
                    const durationHasDiscount = duration.discount_price > 0 && duration.discount_price < duration.original_price;

                    return (
                      <button
                        key={duration.id}
                        type="button"
                        onClick={() => onDurationChange(duration.id)}
                        className={`flex items-center justify-between rounded-lg border p-3 text-left text-sm transition-all duration-200 ${
                          isSelected
                            ? 'border-blue-500 bg-blue-50 ring-1 ring-blue-500'
                            : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`size-5 rounded-full border-2 ${isSelected ? 'border-blue-500 bg-blue-500' : 'border-gray-300'}`}>
                            {isSelected && (
                              <div className="size-full scale-[0.4] rounded-full bg-white" />
                            )}
                          </div>
                          <span className="font-medium text-gray-900">
                            {duration.duration_days}
                            {' '}
                            {t('days') || 'ngày'}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`font-bold ${durationHasDiscount ? 'text-red-600' : 'text-gray-900'}`}>
                            $
                            {durationPrice.toFixed(2)}
                          </span>
                          {durationHasDiscount && (
                            <span className="text-xs text-gray-500 line-through">
                              $
                              {Number(duration.original_price).toFixed(2)}
                            </span>
                          )}
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Features */}
            {features && features.length > 0 && (
              <div className="mb-6">
                <h4 className="mb-3 text-sm font-medium text-gray-700">
                  {t('features') || 'Ghi chú'}
                  :
                </h4>
                <ul className="list-inside list-disc space-y-2 rounded-lg bg-gray-50 p-4 text-sm text-gray-600">
                  {features.map((feature, index) => (
                    <li key={typeof feature === 'string' ? feature : `feature-${index}`} className="pl-1">
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Quantity selector */}
            <div className="mb-6">
              <label className="mb-3 block text-sm font-medium text-gray-700">
                {t('quantity') || 'Số lượng'}
                :
              </label>
              <div className="flex items-center">
                <button
                  type="button"
                  onClick={handleDecrement}
                  disabled={quantity <= 1 || isOutOfStock}
                  className={`flex size-12 items-center justify-center rounded-l-lg border border-gray-300 ${
                    quantity <= 1 || isOutOfStock ? 'cursor-not-allowed bg-gray-100 text-gray-400' : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                  aria-label="Decrease quantity"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="size-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                </button>
                <div className="flex h-12 w-16 items-center justify-center border-y border-gray-300 bg-white text-center text-lg font-medium">
                  {quantity}
                </div>
                <button
                  type="button"
                  onClick={handleIncrement}
                  disabled={quantity >= selectedDuration.quantity || isOutOfStock}
                  className={`flex size-12 items-center justify-center rounded-r-lg border border-gray-300 ${
                    quantity >= selectedDuration.quantity || isOutOfStock ? 'cursor-not-allowed bg-gray-100 text-gray-400' : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                  aria-label="Increase quantity"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="size-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
              {selectedDuration.quantity <= 5 && selectedDuration.quantity > 0 && (
                <div className="mt-2 text-sm text-orange-600">
                  {t('onlyLeft') || 'Chỉ còn'}
                  {' '}
                  {selectedDuration.quantity}
                  {' '}
                  {t('remaining') || 'sản phẩm'}
                </div>
              )}
            </div>

            {/* Add to Cart Button */}
            <button
              type="button"
              onClick={onAddToCart}
              disabled={isOutOfStock}
              className={`w-full rounded-lg px-6 py-4 font-medium text-white transition-all duration-200 ${
                isOutOfStock
                  ? 'cursor-not-allowed bg-gray-400'
                  : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:-translate-y-0.5 hover:from-blue-700 hover:to-blue-800 hover:shadow-lg'
              }`}
            >
              {isOutOfStock ? (t('outOfStock') || 'Hết hàng') : (t('addToCart') || 'Thêm vào giỏ')}
            </button>
          </div>
        </div>

        {/* Description with Markdown */}
        {description && (
          <div className="border-t border-gray-200 p-6">
            <h3 className="mb-4 text-xl font-semibold text-gray-800">{t('description') || 'Mô tả'}</h3>
            <div className="prose prose-sm max-w-none rounded-lg bg-gray-50 p-6">
              <TipTapViewer content={description} markdownMode />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductDetailModal;
