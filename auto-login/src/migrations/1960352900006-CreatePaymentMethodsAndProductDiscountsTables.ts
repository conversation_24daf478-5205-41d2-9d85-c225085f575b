import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePaymentMethodsAndProductDiscounts1960352900002
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create payment_methods table
    await queryRunner.query(`
      CREATE TABLE "payment_methods" (
        "id" SERIAL NOT NULL,
        "name" VARCHAR NOT NULL,
        "description" TEXT,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_payment_methods_name" UNIQUE ("name"),
        CONSTRAINT "PK_payment_methods" PRIMARY KEY ("id")
      )
    `);

    // Create product_discounts table
    await queryRunner.query(`
      CREATE TABLE "product_discounts" (
        "id" SERIAL NOT NULL,
        "product_id" INTEGER NOT NULL,
        "payment_method_id" INTEGER NOT NULL,
        "discount_percent" DECIMAL(5,2) NOT NULL DEFAULT '0',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_product_discounts_product_payment" UNIQUE ("product_id", "payment_method_id"),
        CONSTRAINT "PK_product_discounts" PRIMARY KEY ("id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "product_discounts" 
      ADD CONSTRAINT "FK_product_discounts_product" 
      FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "product_discounts" 
      ADD CONSTRAINT "FK_product_discounts_payment_method" 
      FOREIGN KEY ("payment_method_id") REFERENCES "payment_methods"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Insert default payment methods
    await queryRunner.query(`
      INSERT INTO "payment_methods" ("name", "description") VALUES 
      ('paypal', 'PayPal payment method for secure online transactions'),
      ('cryptomus', 'Cryptomus cryptocurrency payment method')
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints first
    await queryRunner.query(`
      ALTER TABLE "product_discounts" DROP CONSTRAINT "FK_product_discounts_payment_method"
    `);

    await queryRunner.query(`
      ALTER TABLE "product_discounts" DROP CONSTRAINT "FK_product_discounts_product"
    `);

    // Drop tables
    await queryRunner.query(`DROP TABLE "product_discounts"`);
    await queryRunner.query(`DROP TABLE "payment_methods"`);
  }
}
